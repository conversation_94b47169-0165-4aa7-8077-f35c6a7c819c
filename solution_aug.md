# 产品详情页通用模块需求技术方案设计

## 1. 需求分析

### 1.1 业务需求解读

基于requirement.md分析，本次需求主要涉及三个业务领域的功能优化：

#### 1.1.1 行业通用部分
- **开店宝后台翻单引导标签**：需要在后台展示"待升级"和"待更新"标签，涉及眼镜、眼科、口腔等二级类目的团单状态识别

#### 1.1.2 双眼部分（眼镜/眼科）
- **质保信息单位属性扩展**：支持"年"与"天"等单位，兼容现有数据
- **取镜时间类型新增**：支持"立等可取"、"指定天数后可取"、"指定天数内可取"、"指定天数范围可取"等多种类型
- **标题拼接逻辑更新**：模式3调整为模式2，用"｜"替换"|"
- **度数/折射率说明入口及浮层**：为配镜类目补充说明入口
- **镜片技术字段多选支持**：由单选调整为多选，以"、"区隔展示
- **验光操作人员多选支持**：由单选调整为多选，以"/"区隔展示
- **团详数据结构优化**：翻单时直接透传图文详情等信息

#### 1.1.3 口腔部分
- **补牙科普信息补充**：3M材料型号科普信息展示
- **服务时长范围逻辑**：支持范围值展示如"10 - 20分钟"
- **套餐包含新增属性**：新增"套餐包含"字段，支持多选枚举
- **服务时长数据清洗**：指定场景下不显示服务时长

### 1.2 关键功能点识别

1. **数据展示逻辑优化**：多个字段从单选改为多选，需要统一的分隔符处理
2. **浮层数据结构化**：度数/折射率说明、3M材料科普等需要浮层展示
3. **条件化业务规则**：基于类目ID、属性值等条件的展示逻辑
4. **向后兼容性**：确保现有数据在新逻辑下正确处理

### 1.3 约束条件分析

- **架构约束**：必须在现有DDD架构基础上扩展，不能破坏现有模块边界
- **性能约束**：不能影响现有RPC服务的响应性能
- **兼容性约束**：必须保证老数据在新逻辑下的正确展示
- **配置化约束**：复杂业务规则需要支持Lion配置管理

## 2. 方案设计

### 2.1 整体设计思路

基于experience.md中的设计经验，采用**最小化侵入性扩展**策略：

1. **复用现有数据结构**：在`DealDetailStructuredDetailVO`基础上扩展字段
2. **扩展现有Builder组件**：基于Strategy模式新增具体策略实现
3. **配置化管理业务规则**：通过Lion配置管理复杂的展示逻辑
4. **工具类封装通用逻辑**：将重复的处理逻辑抽象为工具方法

### 2.2 核心技术方案

#### 2.2.1 数据结构扩展

**扩展DealDetailStructuredDetailVO**：
- 新增`warrantyUnit`字段：质保单位（年/天）
- 新增`pickupTimeType`字段：取镜时间类型
- 新增`multiSelectSeparator`字段：多选分隔符配置
- 新增`explanationPopup`字段：说明浮层数据
- 新增`packageIncludes`字段：套餐包含信息

#### 2.2.2 Builder组件扩展

**新增专门的Strategy实现**：
- `EyeGlassesStructuredDetailStrategy`：眼镜类目专用策略
- `EyeCareStructuredDetailStrategy`：眼科类目专用策略  
- `DentalStructuredDetailStrategy`：口腔类目专用策略

**扩展现有Builder**：
- 在`ExcelInfoBuilder`中新增度数/折射率说明逻辑
- 在`GlassesKeyInfoStrategy`中新增镜片技术多选处理
- 在`InspectionInfoBuilder`中新增验光操作人员多选处理

#### 2.2.3 工具类设计

**新增FacialDetailUtils工具类**：
```java
public class FacialDetailUtils {
    // 多选字段处理
    public static String formatMultiSelectField(List<String> values, String separator);
    
    // 质保信息格式化
    public static String formatWarrantyInfo(String value, String unit);
    
    // 取镜时间格式化
    public static String formatPickupTime(String type, String value);
    
    // 浮层数据生成
    public static String generatePopupData(String type, Map<String, Object> data);
}
```

**扩展现有工具类**：
- 在`EyesAttrUtils`中新增度数范围处理方法
- 在`GlassesLensAttrUtils`中新增镜片技术多选处理
- 在`DealAttrHelper`中新增通用多选字段处理

### 2.3 具体实现路径

#### 2.3.1 眼镜/眼科功能实现

**质保信息单位支持**：
1. 在`GuaranteeInfoModuleBuilder`中扩展质保信息构建逻辑
2. 新增`WarrantyInfoProcessor`处理器，支持年/天单位转换
3. 通过Lion配置管理不同类目的默认单位

**取镜时间类型扩展**：
1. 在`AvailableTimeHelper`中新增取镜时间类型处理方法
2. 扩展`AvailableTimeStrategy`支持多种时间表达方式
3. 配置化管理不同类目的时间展示规则

**镜片技术多选支持**：
1. 修改`ExcelInfoBuilder`中镜片技术字段处理逻辑
2. 使用"、"作为分隔符连接多个技术选项
3. 新增提报入口的浮层数据生成

**验光操作人员多选**：
1. 在`InspectionInfoBuilder`中修改操作人员字段处理
2. 使用"/"作为分隔符连接多个操作人员
3. 添加可选说明文案

#### 2.3.2 口腔功能实现

**3M材料科普信息**：
1. 新增`DentalMaterialPopupBuilder`构建器
2. 基于Technique和MaterialBrand条件判断是否显示
3. 配置化管理不同材料型号的图片和说明

**服务时长范围支持**：
1. 扩展`ServiceDurationProcessor`支持范围值
2. 智能识别单值和范围值的展示格式
3. 支持"10 - 20分钟"格式的时长展示

**套餐包含属性**：
1. 在相关Builder中新增套餐包含字段处理
2. 支持多选枚举的展示逻辑
3. 配置化管理可选项列表

## 3. 架构适配

### 3.1 现有RPC服务集成

**服务调用链路保持不变**：
```
外部调用 → ProductDetailPageCommonModuleSpiImpl → 模块编排框架 → 扩展后的Builder → 现有Fetcher → 外部服务/缓存
```

**新增组件集成点**：
- 在`CompositeAtomServiceImpl`中集成新的处理器
- 在模块编排框架中注册新的Builder策略
- 在配置服务中新增相关配置项

### 3.2 数据流和调用链路

**数据获取流程**：
1. `ProductAttrFetcher`获取商品属性数据
2. `ProductCategoryFetcher`获取类目信息
3. 新的Strategy根据类目ID选择处理逻辑
4. 工具类处理多选字段和格式化
5. 返回扩展后的VO数据

**缓存策略**：
- 复用现有Redis缓存机制
- 新增配置项支持实时刷新
- 浮层数据支持JSON序列化缓存

### 3.3 配置管理集成

**Lion配置项设计**：
```
# 质保单位配置
product.detail.warranty.unit.mapping = {
  "149000": "天",  // 近视配镜
  "131013": "天",  // 儿童配镜
  "148001": "年"   // 仅镜片
}

# 多选分隔符配置
product.detail.multiselect.separators = {
  "lens_technology": "、",
  "optometry_operator": "/",
  "package_includes": "、"
}

# 3M材料科普配置
product.detail.dental.3m.materials = {
  "Z250": {"image": "url1", "description": "desc1"},
  "Z350": {"image": "url2", "description": "desc2"}
}
```

## 4. 实施计划

### 4.1 开发阶段划分

**第一阶段：基础设施准备（1-2天）**
- 扩展`DealDetailStructuredDetailVO`数据结构
- 新增`FacialDetailUtils`工具类
- 配置Lion配置项

**第二阶段：眼镜/眼科功能开发（3-4天）**
- 实现质保信息单位支持
- 实现取镜时间类型扩展
- 实现镜片技术和验光操作人员多选
- 实现度数/折射率说明浮层

**第三阶段：口腔功能开发（2-3天）**
- 实现3M材料科普信息
- 实现服务时长范围支持
- 实现套餐包含属性

**第四阶段：集成测试和优化（2天）**
- 单元测试和集成测试
- 性能测试和优化
- 兼容性验证

### 4.2 关键开发任务

**核心开发任务**：
1. 数据结构扩展和向后兼容性保证
2. Strategy模式的具体策略实现
3. 工具类方法的通用逻辑封装
4. Lion配置项的设计和管理
5. 浮层数据的JSON序列化处理

**依赖关系**：
- 数据结构扩展 → 所有功能开发
- 工具类开发 → 具体功能实现
- 配置项设计 → 业务逻辑实现
- 单元测试 → 集成测试

### 4.3 风险评估

**技术风险**：
- 多选字段处理的性能影响：通过缓存和批量处理优化
- 浮层数据序列化的复杂度：使用标准JSON格式简化
- 配置项变更的实时生效：依赖现有Lion配置刷新机制

**业务风险**：
- 老数据兼容性问题：提供默认值和容错处理
- 不同类目展示逻辑的复杂性：通过配置化管理降低复杂度
- 前后端数据格式对接：严格按照移动端协议规范

**缓解措施**：
- 充分的单元测试覆盖
- 灰度发布和回滚机制
- 详细的技术文档和操作手册

## 5. 技术细节

### 5.1 类设计和接口定义

**新增Strategy接口**：
```java
public interface FacialDetailStrategy {
    boolean isApplicable(ProductCategory category);
    List<DealDetailStructuredDetailVO> buildDetails(DealDetailBuildContext context);
    String getStrategyName();
}
```

**扩展现有VO类**：
```java
@Data
@Builder
public class DealDetailStructuredDetailVO {
    // 现有字段...
    
    @FieldDoc(description = "质保单位")
    private String warrantyUnit;
    
    @FieldDoc(description = "取镜时间类型")
    private String pickupTimeType;
    
    @FieldDoc(description = "多选分隔符")
    private String multiSelectSeparator;
    
    @FieldDoc(description = "说明浮层数据")
    private String explanationPopup;
    
    @FieldDoc(description = "套餐包含信息")
    private String packageIncludes;
}
```

### 5.2 数据模型和配置要求

**配置数据模型**：
```java
public class FacialDetailConfig {
    private Map<Integer, String> warrantyUnitMapping;
    private Map<String, String> multiSelectSeparators;
    private Map<String, MaterialInfo> dentalMaterials;
    private List<Integer> explanationCategoryIds;
}
```

**浮层数据模型**：
```java
public class PopupData {
    private String type;
    private String title;
    private String content;
    private String imageUrl;
    private Map<String, Object> extraData;
}
```

### 5.3 集成测试和部署考虑

**测试策略**：
- 单元测试：覆盖所有新增工具方法和策略实现
- 集成测试：验证完整的数据流转和RPC调用
- 兼容性测试：确保老数据在新逻辑下正确处理
- 性能测试：验证新增逻辑对响应时间的影响

**部署方案**：
- 采用蓝绿部署，确保服务不中断
- 配置项支持动态刷新，无需重启服务
- 提供回滚机制，快速恢复到原有逻辑
- 监控关键指标，及时发现和处理问题

**监控指标**：
- RPC服务响应时间和成功率
- 新增字段的数据完整性
- 配置项的使用情况和错误率
- 浮层数据的生成和展示效果

通过以上技术方案设计，可以在保持现有架构稳定性的前提下，高效实现所有需求功能，确保系统的可维护性和可扩展性。
